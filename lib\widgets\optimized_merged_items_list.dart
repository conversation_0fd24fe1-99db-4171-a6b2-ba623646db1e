import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/box_widget.dart';
import 'package:bibl/widgets/premium_skeleton_loader.dart';
import 'package:bibl/utils/premium_animations.dart';
import 'package:bibl/utils/lightning_image_preloader.dart';
import 'package:bibl/utils/performance_optimizer.dart';
import 'package:bibl/services/premium_ad_manager.dart';
import 'interests_widget.dart';
import 'premium_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// Ultra-optimized MergedItemsList with zero layout shifts and premium performance
class OptimizedMergedItemsList extends StatefulWidget {
  final bool isForLibrary;
  final ScrollController scrollController;

  const OptimizedMergedItemsList({
    Key? key,
    required this.isForLibrary,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<OptimizedMergedItemsList> createState() =>
      _OptimizedMergedItemsListState();
}

class _OptimizedMergedItemsListState extends State<OptimizedMergedItemsList>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();

  // Lazy initialize ad manager
  PremiumAdManager? _adManager;
  PremiumAdManager get adManager {
    _adManager ??= Get.put(PremiumAdManager());
    return _adManager!;
  }

  // Performance managers
  late ScrollPerformanceOptimizer _scrollOptimizer;
  final MemoryAwareCacheManager<int, Widget> _widgetCache =
      MemoryAwareCacheManager(maxSize: 30);

  // State management
  bool _isLoadingMore = false;
  bool _showScrollToTopButton = false;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  // Constants
  static const double _adHeight =
      280.0; // Total container height including label for square ads
  static const double _itemApproximateHeight = 220.0;
  static const int _preloadAheadItems = 8; // Increased for better preloading

  @override
  void initState() {
    super.initState();
    _initializeComponents();

    // Defer initial ad preloading
    if (!_isPremiumUser) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _preloadInitialAds();
      });
    }
  }

  bool get _isPremiumUser =>
      profileController.userr.value.isPremiumUser ?? false;

  void _initializeComponents() {
    // Initialize animations
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));

    // Initialize scroll optimizer
    _scrollOptimizer = ScrollPerformanceOptimizer(
      onScrollStart: _onScrollStart,
      onScrollEnd: _onScrollEnd,
      onScrollUpdate: _onScrollUpdate,
    );

    // Add scroll listener
    widget.scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    _scrollOptimizer.handleScroll(widget.scrollController.offset);
  }

  void _onScrollStart() {
    // Pause non-critical operations during scroll
    debugPrint('📜 Scroll started');
  }

  void _onScrollEnd() {
    // Resume operations after scroll
    debugPrint('📜 Scroll ended');
    _intelligentPreload();
  }

  void _onScrollUpdate() {
    final offset = widget.scrollController.offset;
    final maxExtent = widget.scrollController.position.maxScrollExtent;

    // Update FAB visibility
    final shouldShowFAB = offset > 300;
    if (shouldShowFAB != _showScrollToTopButton) {
      setState(() {
        _showScrollToTopButton = shouldShowFAB;
      });

      if (shouldShowFAB) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }

    // Load more content when near bottom - increased threshold to prevent aggressive loading
    if (maxExtent > 0 && offset >= maxExtent - 1200 && !_isLoadingMore) {
      // Debounce load more to prevent rapid triggering during scroll
      PerformanceOptimizer.debounce(
        'load_more_content',
        const Duration(milliseconds: 300),
        () => _loadMoreContent(),
      );
    }

    // Clean up distant ads for better memory management
    if (!_isPremiumUser) {
      final currentIndex = (offset / _itemApproximateHeight).floor();
      adManager.cleanupDistantAds('list_ad_', currentIndex);
    }
  }

  void _preloadInitialAds() async {
    // Wait a bit for proper initialization
    await Future.delayed(const Duration(milliseconds: 300));

    final adKeys = <String>[];
    // Preload more ads initially for smoother scrolling
    for (int i = 0; i < 10; i++) {
      adKeys.add('list_ad_$i');
    }

    await adManager.preloadAds(
      adKeys,
      Size(Get.width - 40, 220), // Square ad height (medium rectangle)
    );

    // Debug ad stats in development
    if (kDebugMode) {
      final stats = adManager.getCacheStats();
      debugPrint('📊 Ad Stats after preload: ${stats.toString()}');
    }
  }

  void _intelligentPreload() {
    PerformanceOptimizer.runWhenIdle(() {
      _preloadImages();
      _preloadUpcomingAds();
    });
  }

  void _preloadImages() {
    try {
      final offset = widget.scrollController.offset;
      final viewportHeight = MediaQuery.of(context).size.height;

      final firstVisible = (offset / _itemApproximateHeight).floor();
      final lastVisible =
          ((offset + viewportHeight) / _itemApproximateHeight).ceil();

      final items = _getItems();
      final visibleUrls = <String>[];
      final upcomingUrls = <String>[];

      // Collect URLs for visible items
      for (int i = firstVisible; i <= lastVisible && i < items.length; i++) {
        final url = _getImageUrl(items[i]);
        if (url != null) visibleUrls.add(url);
      }

      // Collect URLs for upcoming items
      for (int i = lastVisible + 1;
          i < items.length && i <= lastVisible + _preloadAheadItems;
          i++) {
        final url = _getImageUrl(items[i]);
        if (url != null) upcomingUrls.add(url);
      }

      if (visibleUrls.isNotEmpty || upcomingUrls.isNotEmpty) {
        LightningImagePreloader.smartPreload(visibleUrls, upcomingUrls);
      }
    } catch (e) {
      debugPrint('Error preloading images: $e');
    }
  }

  void _preloadUpcomingAds() {
    if (_isPremiumUser) return;

    final offset = widget.scrollController.offset;
    final currentIndex = (offset / _itemApproximateHeight).floor();

    // Preload next 10 ad positions ahead for smoother scrolling
    final adKeys = <String>[];
    for (int i = 1; i <= 10; i++) {
      final adIndex = ((currentIndex + i * 3) ~/ 4);
      final adKey = 'list_ad_$adIndex';

      // Always try to load ads ahead, even if they might be loading
      if (adManager.getAdState(adKey) != AdLoadState.loaded) {
        adKeys.add(adKey);
      }
    }

    if (adKeys.isNotEmpty) {
      adManager.preloadAds(
        adKeys,
        Size(Get.width - 40, 220), // Square ad height (medium rectangle)
      );
    }
  }

  String? _getImageUrl(dynamic item) {
    if (item is LessonModel) return item.imageLink;
    if (item is QuizModel) return item.quizImageLink;
    return null;
  }

  Future<void> _loadMoreContent() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    try {
      await PerformanceOptimizer.measureOperation(
        'Load More Content',
        () async {
          if (widget.isForLibrary) {
            lessonController.loadMoreLibraryDisplayItems();
          } else {
            lessonController.loadMoreHomeDisplayItems();
          }

          await Future.delayed(const Duration(milliseconds: 200));
        },
      );
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  Widget _buildOptimizedAd(int position) {
    final adKey = 'list_ad_${position ~/ 4}';

    // NEVER cache ad widgets to avoid reuse issues
    // Each ad position must create a new widget instance

    return RepaintBoundary(
      key: ValueKey('ad_$position'), // Unique key for each ad position
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 10, 16, 20),
        height: _adHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 24,
              spreadRadius: 0,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Ad label
            Container(
              height: 35,
              alignment: Alignment.center,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'REKLAMA',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
            // Ad content with fixed dimensions to prevent layout shifts
            Expanded(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: SizedBox(
                    width: Get.width - 64,
                    height: 220, // Fixed height to prevent layout jumps
                    child: PremiumAdWidget(
                      key: ValueKey(
                          'ad_widget_$adKey'), // Unique key for ad widget
                      adKey: adKey,
                      width: Get.width - 64,
                      height:
                          220, // Square ad height (medium rectangle 300x250)
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptimizedItem(int index) {
    final items = _getItems();
    if (index >= items.length) return const SizedBox.shrink();

    final item = items[index];
    final itemKey = item.hashCode;

    // Check cache
    final cached = _widgetCache.get(itemKey);
    if (cached != null) return cached;

    final itemWidget = RepaintBoundary(
      key: ValueKey('item_$itemKey'),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: BoxWidget(
          lesson: item is LessonModel ? item : null,
          quiz: item is QuizModel ? item : null,
          shuffleQuiz: item is ShuffleQuizModel ? item : null,
        ),
      ),
    );

    _widgetCache.put(itemKey, itemWidget);
    return itemWidget;
  }

  bool _isAdPosition(int index) => (index + 1) % 4 == 0;

  int _calculateItemIndex(int visualIndex) {
    if (_isPremiumUser) return visualIndex;
    return visualIndex - (visualIndex ~/ 4);
  }

  int _getTotalChildCount() {
    final itemCount = _getItemCount();
    if (_isPremiumUser) return itemCount;
    return itemCount + (itemCount ~/ 3);
  }

  int _getItemCount() {
    return widget.isForLibrary
        ? lessonController.libraryDisplayedItems.length
        : lessonController.homeDisplayedItems.length;
  }

  List<dynamic> _getItems() {
    return widget.isForLibrary
        ? lessonController.libraryDisplayedItems
        : lessonController.homeDisplayedItems;
  }

  void _scrollToTop() {
    widget.scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
    );
  }

  Future<void> _onRefresh() async {
    // Clear caches
    _widgetCache.clear();

    // Refresh content
    await PerformanceOptimizer.measureOperation(
      'Refresh Content',
      () async {
        if (widget.isForLibrary) {
          lessonController.shuffleAllItems(
            isShuffle: true,
            shouldClear: true,
            from: 'optimized list library refresh',
          );
        } else {
          lessonController.mergeAndShuffleItems(
            isShuffle: true,
            from: 'optimized list home refresh',
            shouldClear: true,
          );
        }

        await Future.delayed(const Duration(milliseconds: 300));
      },
    );

    // Preload ads again
    if (!_isPremiumUser) {
      _preloadInitialAds();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Obx(() {
      final items = _getItems();

      if (items.isEmpty && profileController.isUserDataLoading.value) {
        return const PremiumSkeletonLoader.list(itemCount: 5);
      }

      return Stack(
        children: [
          RefreshIndicator(
            color: mainColor,
            onRefresh: _onRefresh,
            child: CustomScrollView(
              controller: widget.scrollController,
              physics: const ClampingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics(),
              ),
              cacheExtent: 500,
              slivers: [
                // Interests header
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      interestsWidget(context, widget.isForLibrary),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),

                // Main list
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      if (!_isPremiumUser && _isAdPosition(index)) {
                        return _buildOptimizedAd(index);
                      }

                      final itemIndex = _calculateItemIndex(index);
                      if (itemIndex >= _getItemCount()) {
                        return const SizedBox.shrink();
                      }

                      return _buildOptimizedItem(itemIndex);
                    },
                    childCount: _getTotalChildCount(),
                    addAutomaticKeepAlives: true,
                    addRepaintBoundaries: true,
                  ),
                ),

                // Loading indicator
                if (_isLoadingMore)
                  const SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Center(
                        child: PremiumLoadingIndicator(),
                      ),
                    ),
                  ),

                // Bottom padding
                const SliverPadding(
                  padding: EdgeInsets.only(bottom: 100),
                ),
              ],
            ),
          ),

          // FAB
          if (_showScrollToTopButton)
            Positioned(
              right: 16,
              bottom: 16,
              child: ScaleTransition(
                scale: _fabScaleAnimation,
                child: FloatingActionButton(
                  onPressed: _scrollToTop,
                  backgroundColor: mainColor,
                  elevation: 8,
                  child: const Icon(
                    Icons.keyboard_arrow_up,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    // Clean up all resources
    widget.scrollController.removeListener(_handleScroll);
    _scrollOptimizer.dispose();
    _fabAnimationController.dispose();
    _widgetCache.clear();
    PerformanceOptimizer.cancelAll();

    super.dispose();
  }
}
